{"name": "html-geolocation-test", "version": "1.0.0", "description": "Testing setup for HTML geolocation application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js 8000", "test": "node test-automation.js", "test:manual": "echo 'Open http://localhost:8000/geonorm.html and follow the manual testing checklist in SETUP_INSTRUCTIONS.md'", "test:install": "npm install playwright && npx playwright install", "test:results": "echo 'View test results at http://localhost:8000/test-results.html'"}, "keywords": ["html", "geolocation", "testing", "maps"], "author": "", "license": "MIT", "devDependencies": {"playwright": "^1.40.0"}, "engines": {"node": ">=12.0.0"}}