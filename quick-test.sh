#!/bin/bash

# Quick Test Script for HTML Geolocation App
# This script helps you quickly set up and test your application

echo "🚀 HTML Geolocation Quick Test Setup"
echo "===================================="

# Check if we're in the right directory
if [ ! -f "geonorm.html" ]; then
    echo "❌ Error: geonorm.html not found in current directory"
    echo "Please run this script from the project root directory"
    exit 1
fi

echo "✅ Found geonorm.html"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for Python
if command_exists python3; then
    echo "✅ Python 3 found"
    PYTHON_AVAILABLE=true
else
    echo "⚠️  Python 3 not found"
    PYTHON_AVAILABLE=false
fi

# Check for Node.js
if command_exists node; then
    echo "✅ Node.js found"
    NODE_AVAILABLE=true
else
    echo "⚠️  Node.js not found"
    NODE_AVAILABLE=false
fi

# If neither is available, exit
if [ "$PYTHON_AVAILABLE" = false ] && [ "$NODE_AVAILABLE" = false ]; then
    echo "❌ Error: Neither Python 3 nor Node.js found"
    echo "Please install Python 3 or Node.js to run the test server"
    exit 1
fi

echo ""
echo "📋 Quick Test Options:"
echo "1. Start Python server and open browser"
echo "2. Start Node.js server and open browser"
echo "3. Run automated tests (requires Node.js and Playwright)"
echo "4. View manual testing checklist"
echo "5. Check test results"
echo ""

read -p "Choose an option (1-5): " choice

case $choice in
    1)
        if [ "$PYTHON_AVAILABLE" = true ]; then
            echo "🐍 Starting Python server..."
            echo "Server will run at http://localhost:8000"
            echo "Press Ctrl+C to stop the server"
            echo ""
            echo "📖 Manual testing checklist: MANUAL_TESTING_CHECKLIST.md"
            echo "🔧 Setup instructions: SETUP_INSTRUCTIONS.md"
            echo ""
            python3 server.py
        else
            echo "❌ Python 3 not available"
        fi
        ;;
    2)
        if [ "$NODE_AVAILABLE" = true ]; then
            echo "🟢 Starting Node.js server..."
            echo "Server will run at http://localhost:8000"
            echo "Press Ctrl+C to stop the server"
            echo ""
            echo "📖 Manual testing checklist: MANUAL_TESTING_CHECKLIST.md"
            echo "🔧 Setup instructions: SETUP_INSTRUCTIONS.md"
            echo ""
            node server.js
        else
            echo "❌ Node.js not available"
        fi
        ;;
    3)
        if [ "$NODE_AVAILABLE" = true ]; then
            echo "🤖 Setting up automated tests..."
            
            # Check if playwright is installed
            if [ ! -d "node_modules/playwright" ]; then
                echo "📦 Installing Playwright..."
                npm install playwright
                npx playwright install
            fi
            
            echo "🧪 Running automated tests..."
            echo "Make sure your server is running in another terminal:"
            echo "  python3 server.py  OR  node server.js"
            echo ""
            read -p "Press Enter when server is ready, or Ctrl+C to cancel..."
            
            node test-automation.js
        else
            echo "❌ Node.js required for automated tests"
        fi
        ;;
    4)
        echo "📖 Opening manual testing checklist..."
        if command_exists cat; then
            cat MANUAL_TESTING_CHECKLIST.md
        else
            echo "Please open MANUAL_TESTING_CHECKLIST.md in your text editor"
        fi
        ;;
    5)
        echo "📊 Test results:"
        if [ -f "received_locations.json" ]; then
            echo "Location data received:"
            cat received_locations.json
            echo ""
            echo "🌐 View detailed results at: http://localhost:8000/test-results.html"
            echo "(Make sure server is running)"
        else
            echo "No test results found yet. Run some tests first!"
        fi
        ;;
    *)
        echo "❌ Invalid option. Please choose 1-5."
        ;;
esac

echo ""
echo "📚 Additional Resources:"
echo "  • Setup Instructions: SETUP_INSTRUCTIONS.md"
echo "  • Manual Testing: MANUAL_TESTING_CHECKLIST.md"
echo "  • Test your app: http://localhost:8000/geonorm.html"
echo "  • View results: http://localhost:8000/test-results.html"
