<!DOCTYPE html>
<html>
<head>
    <title>Test Results Viewer</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .location-entry {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
        }
        .coordinates {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .refresh-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        .refresh-btn:hover {
            background-color: #0056b3;
        }
        .no-data {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 40px;
        }
        .map-link {
            color: #007bff;
            text-decoration: none;
        }
        .map-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Location Test Results</h1>
        <p>This page shows the location data received from your HTML geolocation test.</p>
        
        <button class="refresh-btn" onclick="loadResults()">Refresh Results</button>
        
        <div id="results">
            <div class="no-data">No location data received yet. Test your application first!</div>
        </div>
    </div>

    <script>
        async function loadResults() {
            try {
                const response = await fetch('/api/test-results');
                const data = await response.json();
                
                const resultsDiv = document.getElementById('results');
                
                if (data.length === 0) {
                    resultsDiv.innerHTML = '<div class="no-data">No location data received yet. Test your application first!</div>';
                    return;
                }
                
                let html = '';
                data.forEach((entry, index) => {
                    const mapUrl = `https://www.google.com/maps?q=${entry.latitude},${entry.longitude}`;
                    html += `
                        <div class="location-entry">
                            <h3>Test #${data.length - index}</h3>
                            <div class="timestamp">Received: ${new Date(entry.timestamp).toLocaleString()}</div>
                            <div class="coordinates">
                                Latitude: ${entry.latitude}<br>
                                Longitude: ${entry.longitude}
                            </div>
                            <a href="${mapUrl}" target="_blank" class="map-link">View on Google Maps</a>
                        </div>
                    `;
                });
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                console.error('Error loading results:', error);
                document.getElementById('results').innerHTML = 
                    '<div class="no-data">Error loading results. Make sure the server is running.</div>';
            }
        }
        
        // Load results when page loads
        loadResults();
        
        // Auto-refresh every 10 seconds
        setInterval(loadResults, 10000);
    </script>
</body>
</html>
