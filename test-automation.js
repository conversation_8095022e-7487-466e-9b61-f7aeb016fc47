/**
 * Automated testing for HTML geolocation application
 * 
 * This script uses <PERSON><PERSON> to automate browser testing.
 * Install with: npm install -D playwright
 * Run with: node test-automation.js
 */

const { chromium, firefox, webkit } = require('playwright');

class GeolocationTester {
    constructor() {
        this.testResults = [];
        this.serverUrl = 'http://localhost:8000';
    }

    async runAllTests() {
        console.log('🚀 Starting automated geolocation tests...\n');
        
        // Test with different browsers
        const browsers = [
            { name: 'Chromium', launcher: chromium },
            { name: 'Firefox', launcher: firefox },
            { name: 'WebKit', launcher: webkit }
        ];

        for (const browser of browsers) {
            try {
                console.log(`Testing with ${browser.name}...`);
                await this.testBrowser(browser);
            } catch (error) {
                console.error(`❌ Error testing ${browser.name}:`, error.message);
                this.testResults.push({
                    browser: browser.name,
                    status: 'failed',
                    error: error.message
                });
            }
        }

        this.printSummary();
    }

    async testBrowser(browserConfig) {
        const browser = await browserConfig.launcher.launch({ headless: false });
        const context = await browser.newContext({
            // Grant geolocation permissions
            permissions: ['geolocation'],
            // Mock geolocation to a test location (Atlanta)
            geolocation: { latitude: 33.7490, longitude: -84.3880 }
        });

        const page = await context.newPage();
        
        try {
            // Navigate to the test page
            await page.goto(`${this.serverUrl}/geonorm.html`);
            
            // Test 1: Page loads correctly
            await this.testPageLoad(page, browserConfig.name);
            
            // Test 2: Find location button works
            await this.testFindLocationButton(page, browserConfig.name);
            
            // Test 3: Form submission works
            await this.testFormSubmission(page, browserConfig.name);
            
            console.log(`✅ ${browserConfig.name} tests completed successfully\n`);
            
        } catch (error) {
            console.error(`❌ ${browserConfig.name} test failed:`, error.message);
            throw error;
        } finally {
            await browser.close();
        }
    }

    async testPageLoad(page, browserName) {
        console.log(`  📄 Testing page load...`);
        
        // Check if title is correct
        const title = await page.title();
        if (title !== 'Share Your Location') {
            throw new Error(`Wrong page title: ${title}`);
        }
        
        // Check if main elements are present
        const findButton = await page.$('#findMeButton');
        const map = await page.$('#map');
        const message = await page.$('#message');
        
        if (!findButton || !map || !message) {
            throw new Error('Required page elements not found');
        }
        
        console.log(`    ✅ Page loaded correctly`);
    }

    async testFindLocationButton(page, browserName) {
        console.log(`  🎯 Testing find location functionality...`);
        
        // Click the find location button
        await page.click('#findMeButton');
        
        // Wait for the location to be found (message should change)
        await page.waitForFunction(() => {
            const message = document.getElementById('message');
            return message && message.textContent.includes('We found you!');
        }, { timeout: 10000 });
        
        // Check if confirm button appears
        const confirmButton = await page.$('#confirmButton');
        const isVisible = await confirmButton.isVisible();
        
        if (!isVisible) {
            throw new Error('Confirm button did not appear after finding location');
        }
        
        // Check if coordinates are filled
        const latitude = await page.inputValue('#latitude');
        const longitude = await page.inputValue('#longitude');
        
        if (!latitude || !longitude) {
            throw new Error('Coordinates not filled in hidden form fields');
        }
        
        console.log(`    ✅ Location found: ${latitude}, ${longitude}`);
    }

    async testFormSubmission(page, browserName) {
        console.log(`  📤 Testing form submission...`);
        
        // Click the confirm button
        await page.click('#confirmButton');
        
        // Wait for the success page
        await page.waitForURL(/.*/, { timeout: 10000 });
        
        // Check if we're on the success page
        const content = await page.textContent('body');
        if (!content.includes('Location Received Successfully!')) {
            throw new Error('Form submission did not redirect to success page');
        }
        
        console.log(`    ✅ Form submitted successfully`);
        
        this.testResults.push({
            browser: browserName,
            status: 'passed',
            timestamp: new Date().toISOString()
        });
    }

    printSummary() {
        console.log('\n📊 Test Summary:');
        console.log('================');
        
        const passed = this.testResults.filter(r => r.status === 'passed').length;
        const failed = this.testResults.filter(r => r.status === 'failed').length;
        
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📊 Total: ${this.testResults.length}`);
        
        if (failed > 0) {
            console.log('\nFailed tests:');
            this.testResults
                .filter(r => r.status === 'failed')
                .forEach(r => console.log(`  - ${r.browser}: ${r.error}`));
        }
        
        console.log('\n🎉 Automated testing complete!');
    }
}

// Check if server is running before starting tests
async function checkServer() {
    try {
        const http = require('http');
        return new Promise((resolve, reject) => {
            const req = http.get('http://localhost:8000', (res) => {
                resolve(true);
            });
            req.on('error', () => reject(false));
            req.setTimeout(5000, () => reject(false));
        });
    } catch (error) {
        return false;
    }
}

// Main execution
async function main() {
    console.log('🔍 Checking if server is running...');
    
    try {
        await checkServer();
        console.log('✅ Server is running\n');
    } catch (error) {
        console.error('❌ Server is not running on http://localhost:8000');
        console.log('Please start the server first:');
        console.log('  node server.js');
        console.log('  or');
        console.log('  python3 server.py');
        process.exit(1);
    }
    
    const tester = new GeolocationTester();
    await tester.runAllTests();
}

// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = GeolocationTester;
