#!/usr/bin/env node
/**
 * Simple HTTP server for testing HTML files with geolocation.
 * Serves files over HTTP on localhost, which allows geolocation to work.
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const querystring = require('querystring');

// MIME types for different file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.ico': 'image/x-icon',
    '.svg': 'image/svg+xml',
};

function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

function serveFile(res, filePath) {
    fs.readFile(filePath, (err, content) => {
        if (err) {
            if (err.code === 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 Not Found</h1>');
            } else {
                res.writeHead(500);
                res.end('Server Error');
            }
        } else {
            res.writeHead(200, { 
                'Content-Type': getMimeType(filePath),
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type'
            });
            res.end(content);
        }
    });
}

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    const pathname = parsedUrl.pathname;

    if (req.method === 'GET' && pathname === '/api/test-results') {
        // Serve test results as JSON
        fs.readFile('received_locations.json', 'utf8', (err, data) => {
            if (err) {
                res.writeHead(200, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                });
                res.end('[]'); // Return empty array if file doesn't exist
                return;
            }

            // Parse each line as JSON and return as array
            const lines = data.trim().split('\n').filter(line => line.trim());
            const results = lines.map(line => {
                try {
                    return JSON.parse(line);
                } catch (e) {
                    return null;
                }
            }).filter(item => item !== null);

            res.writeHead(200, {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            });
            res.end(JSON.stringify(results));
        });
    } else if (req.method === 'POST' && pathname === '/save_location') {
        // Handle location submission
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        
        req.on('end', () => {
            const parsedData = querystring.parse(body);
            const latitude = parsedData.latitude || '';
            const longitude = parsedData.longitude || '';
            
            console.log('Received location data:');
            console.log(`  Latitude: ${latitude}`);
            console.log(`  Longitude: ${longitude}`);
            
            // Save to file for testing purposes
            const locationData = {
                latitude: latitude,
                longitude: longitude,
                timestamp: new Date().toISOString()
            };
            
            fs.appendFile('received_locations.json', JSON.stringify(locationData) + '\n', (err) => {
                if (err) console.error('Error saving location data:', err);
            });
            
            // Send success response
            const responseHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Location Received</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
                    .success { color: #34A853; }
                </style>
            </head>
            <body>
                <h2 class="success">Location Received Successfully!</h2>
                <p>Latitude: ${latitude}</p>
                <p>Longitude: ${longitude}</p>
                <p><a href="/">Go back to test again</a></p>
            </body>
            </html>
            `;
            
            res.writeHead(200, { 
                'Content-Type': 'text/html',
                'Access-Control-Allow-Origin': '*'
            });
            res.end(responseHtml);
        });
    } else if (req.method === 'GET') {
        // Serve static files
        let filePath = pathname === '/' ? '/geonorm.html' : pathname;
        filePath = path.join(__dirname, filePath);
        
        // Security check - prevent directory traversal
        if (!filePath.startsWith(__dirname)) {
            res.writeHead(403);
            res.end('Forbidden');
            return;
        }
        
        serveFile(res, filePath);
    } else {
        res.writeHead(405, { 'Content-Type': 'text/html' });
        res.end('<h1>405 Method Not Allowed</h1>');
    }
});

const PORT = process.argv[2] || 8000;

server.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}/`);
    console.log(`Open http://localhost:${PORT}/geonorm.html to test your HTML file`);
    console.log('Press Ctrl+C to stop the server');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`Port ${PORT} is already in use. Trying port ${parseInt(PORT) + 1}...`);
        server.listen(parseInt(PORT) + 1);
    } else {
        console.error('Server error:', err);
    }
});
