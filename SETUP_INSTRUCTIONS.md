# HTML Geolocation Testing Setup

This guide will help you set up and test your HTML geolocation application.

## Prerequisites

- Python 3.x OR Node.js (for running the local server)
- A Google Maps API key (for maps functionality)
- A modern web browser (Chrome, Firefox, Safari, Edge)

## Step 1: Get a Google Maps API Key

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the "Maps JavaScript API"
4. Go to "Credentials" and create an API key
5. (Optional but recommended) Restrict the API key to your domain/localhost

### For Testing Purposes (Quick Setup)
If you just want to test quickly without setting up a full API key:
1. You can use the demo/development key, but maps may not load properly
2. Or temporarily use an unrestricted API key for localhost testing

## Step 2: Configure Your API Key

Replace `YOUR_API_KEY` in `geonorm.html` with your actual API key:

```html
<script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_ACTUAL_API_KEY&callback=initMap"></script>
```

## Step 3: Start the Local Server

### Option A: Python Server (Recommended)
```bash
python3 server.py
```
Or specify a custom port:
```bash
python3 server.py 8080
```

### Option B: Node.js Server
```bash
node server.js
```
Or specify a custom port:
```bash
node server.js 8080
```

### Option C: Simple Python HTTP Server (Basic)
If you don't need the form submission handling:
```bash
python3 -m http.server 8000
```

## Step 4: Test Your Application

1. Open your browser and go to: `http://localhost:8000/geonorm.html`
2. Click "Find My Exact Location"
3. Allow location permissions when prompted
4. Verify the map centers on your location
5. Click "Confirm & Send Location" to test form submission

## Testing Scenarios

### Manual Testing Checklist

#### Basic Functionality
- [ ] Page loads without errors
- [ ] Map displays (requires valid API key)
- [ ] "Find My Exact Location" button is visible
- [ ] Clicking the button prompts for location permission

#### Geolocation Success
- [ ] After allowing location, map centers on user location
- [ ] A marker appears at the user's location
- [ ] Message updates to "We found you! Please check the pin..."
- [ ] "Find My Exact Location" button hides
- [ ] "Confirm & Send Location" button appears
- [ ] Form submission works (check server logs)

#### Error Handling
- [ ] Denying location permission shows appropriate error message
- [ ] Works when location services are disabled
- [ ] Handles network errors gracefully

#### Browser Compatibility
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari (macOS/iOS)
- [ ] Edge

#### Mobile Testing
- [ ] Responsive design works on mobile
- [ ] Touch interactions work properly
- [ ] GPS accuracy is reasonable on mobile devices

## Troubleshooting

### Common Issues

1. **Maps don't load**
   - Check if your API key is valid
   - Ensure the Maps JavaScript API is enabled
   - Check browser console for errors

2. **Geolocation doesn't work**
   - Must be served over HTTPS or localhost
   - User must grant location permissions
   - Some browsers block geolocation in private/incognito mode

3. **Form submission fails**
   - Check if the server is running
   - Verify the form action URL matches your server endpoint
   - Check server logs for errors

4. **CORS errors**
   - The provided servers include CORS headers
   - If using a different server, ensure CORS is configured

### Server Logs

The test servers will log received location data:
```
Received location data:
  Latitude: 33.7490
  Longitude: -84.3880
```

Location data is also saved to `received_locations.json` for inspection.

## Security Notes

- Never commit API keys to version control
- Use environment variables for API keys in production
- Restrict API keys to specific domains/IPs
- The test servers are for development only - not production ready

## Next Steps

After basic testing works:
1. Set up automated testing (see automated testing setup)
2. Test on different devices and browsers
3. Implement proper error handling
4. Add loading states and better UX
5. Set up proper backend endpoint for production
