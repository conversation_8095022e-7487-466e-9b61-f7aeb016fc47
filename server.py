#!/usr/bin/env python3
"""
Simple HTTP server for testing HTML files with geolocation.
Serves files over HTTP on localhost, which allows geolocation to work.
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse, parse_qs
import json

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_POST(self):
        """Handle POST requests to simulate the location submission endpoint"""
        if self.path == '/save_location':
            # Get the content length
            content_length = int(self.headers['Content-Length'])
            # Read the POST data
            post_data = self.rfile.read(content_length).decode('utf-8')
            
            # Parse the form data
            parsed_data = parse_qs(post_data)
            latitude = parsed_data.get('latitude', [''])[0]
            longitude = parsed_data.get('longitude', [''])[0]
            
            print(f"Received location data:")
            print(f"  Latitude: {latitude}")
            print(f"  Longitude: {longitude}")
            
            # Save to a file for testing purposes
            location_data = {
                'latitude': latitude,
                'longitude': longitude,
                'timestamp': str(datetime.now())
            }
            
            with open('received_locations.json', 'a') as f:
                f.write(json.dumps(location_data) + '\n')
            
            # Send a success response
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            
            response_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Location Received</title>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }}
                    .success {{ color: #34A853; }}
                </style>
            </head>
            <body>
                <h2 class="success">Location Received Successfully!</h2>
                <p>Latitude: {latitude}</p>
                <p>Longitude: {longitude}</p>
                <p><a href="/">Go back to test again</a></p>
            </body>
            </html>
            """
            self.wfile.write(response_html.encode())
        else:
            # For other POST requests, return 404
            self.send_error(404, "Endpoint not found")
    
    def end_headers(self):
        # Add CORS headers to allow cross-origin requests if needed
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def run_server(port=8000):
    """Run the HTTP server on the specified port"""
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print(f"Server running at http://localhost:{port}/")
            print(f"Open http://localhost:{port}/geonorm.html to test your HTML file")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"Port {port} is already in use. Trying port {port + 1}...")
            run_server(port + 1)
        else:
            print(f"Error starting server: {e}")

if __name__ == "__main__":
    import datetime
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8000.")
    
    run_server(port)
