{"name": "geolocation-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server.js", "start": "npm run build && npm run server", "dev:full": "concurrently \"npm run dev\" \"npm run server\"", "test": "echo 'No tests specified yet'"}, "dependencies": {"@googlemaps/react-wrapper": "^1.2.0", "cors": "^2.8.5", "express": "^5.1.0", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "concurrently": "^9.2.1", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}