#!/usr/bin/env node
/**
 * Backend server for the React geolocation app
 * Handles CORS and form submissions
 */

import express from 'express';
import cors from 'cors';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the dist directory (for production)
app.use(express.static(path.join(__dirname, 'dist')));

// Location submission endpoint
app.post('/save_location', (req, res) => {
  const { latitude, longitude } = req.body;
  
  console.log('Received location data:');
  console.log(`  Latitude: ${latitude}`);
  console.log(`  Longitude: ${longitude}`);
  
  // Save to file for testing purposes
  const locationData = {
    latitude: latitude,
    longitude: longitude,
    timestamp: new Date().toISOString(),
    userAgent: req.get('User-Agent'),
    ip: req.ip
  };
  
  const logFile = path.join(__dirname, 'received_locations.json');
  fs.appendFile(logFile, JSON.stringify(locationData) + '\n', (err) => {
    if (err) {
      console.error('Error saving location data:', err);
      return res.status(500).json({ error: 'Failed to save location data' });
    }
    
    console.log('Location data saved successfully');
    res.json({ 
      success: true, 
      message: 'Location received successfully',
      data: {
        latitude: latitude,
        longitude: longitude,
        timestamp: locationData.timestamp
      }
    });
  });
});

// API endpoint to get test results
app.get('/api/test-results', (req, res) => {
  const logFile = path.join(__dirname, 'received_locations.json');
  
  fs.readFile(logFile, 'utf8', (err, data) => {
    if (err) {
      return res.json([]); // Return empty array if file doesn't exist
    }
    
    // Parse each line as JSON and return as array
    const lines = data.trim().split('\n').filter(line => line.trim());
    const results = lines.map(line => {
      try {
        return JSON.parse(line);
      } catch (e) {
        return null;
      }
    }).filter(item => item !== null);
    
    res.json(results);
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Basic route for testing
app.get('/', (req, res) => {
  res.json({ message: 'Geolocation API Server', status: 'running' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running at http://localhost:${PORT}`);
  console.log(`📍 Location endpoint: http://localhost:${PORT}/save_location`);
  console.log(`📊 Test results: http://localhost:${PORT}/api/test-results`);
  console.log('Press Ctrl+C to stop the server');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('\nReceived SIGINT, shutting down gracefully');
  process.exit(0);
});
