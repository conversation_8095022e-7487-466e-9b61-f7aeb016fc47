# 🚀 React Geolocation App - Setup Guide

## Quick Start

### 1. Set up your Google Maps API Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the "Maps JavaScript API"
4. Create credentials (API Key)
5. Copy your API key

### 2. Configure Environment Variables

Copy the example environment file:
```bash
cp .env.example .env
```

Edit `.env` and replace `YOUR_API_KEY_HERE` with your actual Google Maps API key:
```env
VITE_GOOGLE_MAPS_API_KEY=your_actual_google_maps_api_key_here
```

### 3. Install Dependencies

```bash
npm install
```

### 4. Start Development

Run both frontend and backend:
```bash
npm run dev:full
```

This will start:
- Frontend (React + Vite) on http://localhost:5173
- Backend (Express) on http://localhost:3001

### 5. Test the Application

1. Open http://localhost:5173 in your browser
2. Click "Find My Exact Location"
3. Allow location permissions when prompted
4. Verify the map shows your location
5. Click "Confirm & Send Location"
6. Check that you see the success page

## Alternative Commands

### Run Frontend Only
```bash
npm run dev
```

### Run Backend Only
```bash
npm run server
```

### Production Build
```bash
npm run build
npm start
```

## Troubleshooting

### Maps Don't Load
- Check that your API key is correct in `.env`
- Verify Maps JavaScript API is enabled in Google Cloud Console
- Check browser console for errors

### Geolocation Doesn't Work
- Make sure you're using localhost or HTTPS
- Allow location permissions when prompted
- Check that location services are enabled on your device

### Form Submission Fails
- Ensure the backend server is running on port 3001
- Check the browser's network tab for failed requests

## Project Structure

```
geolocation-app/
├── src/
│   ├── components/Map.jsx    # Google Maps component
│   ├── hooks/useGeolocation.js # Geolocation hook
│   ├── App.jsx               # Main app component
│   └── ...
├── server.js                 # Express backend
├── .env                      # Your environment variables
├── .env.example              # Environment template
└── package.json              # Dependencies and scripts
```

## Features

✨ **Modern React App** - Built with Vite for fast development
🗺️ **Google Maps Integration** - Interactive maps with custom markers
📱 **Responsive Design** - Works on desktop and mobile
🎨 **Beautiful UI** - Modern gradient design with smooth animations
🔒 **Secure** - Environment-based API key management
⚡ **Fast** - Optimized for performance

## Need Help?

Check the main README.md for detailed documentation, or review the troubleshooting section above.
