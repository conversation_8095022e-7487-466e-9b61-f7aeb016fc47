<!DOCTYPE html>
<html>
<head>
    <title>Share Your Location</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="http://googleusercontent.com/maps.google.com/9"></script>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; }
        #map { height: 300px; width: 100%; margin-bottom: 15px; background-color: #EFEFEF; }
        .button {
            background-color: #4285F4; /* Google Blue */
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            width: 90%;
            max-width: 350px;
        }
        #confirmButton {
            background-color: #34A853; /* Google Green */
            display: none; /* Hidden at first */
            margin-top: 10px;
        }
        #message {
            margin: 15px;
            font-size: 16px;
        }
    </style>
</head>
<body>

    <h3>Confirm Your Delivery Location</h3>
    <p id="message">Please click the button below so we can find your precise location using your phone's GPS.</p>
    
    <div id="map"></div>

    <button id="findMeButton" class="button">Find My Exact Location</button>

    <form id="locationForm" action="https://yourcompany.com/save_location" method="post">
        <input type="hidden" id="latitude" name="latitude" />
        <input type="hidden" id="longitude" name="longitude" />
        <button type="submit" id="confirmButton" class="button">Confirm & Send Location</button>
    </form>

    <script>
        // Get references to the HTML elements
        const mapDiv = document.getElementById('map');
        const findMeButton = document.getElementById('findMeButton');
        const confirmButton = document.getElementById('confirmButton');
        const latInput = document.getElementById('latitude');
        const lonInput = document.getElementById('longitude');
        const message = document.getElementById('message');
        
        let map;
        let marker;

        // Step 1: Initialize the map to a default location (Atlanta)
        function initMap() {
            const atlanta = { lat: 33.7490, lng: -84.3880 };
            map = new google.maps.Map(mapDiv, {
                zoom: 12,
                center: atlanta,
            });
        }

        // Step 2: Add a click event to the "Find Me" button
        findMeButton.addEventListener('click', () => {
            message.textContent = "Finding your location...";
            
            // Check if Geolocation is supported by the browser
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(geolocationSuccess, geolocationError);
            } else {
                message.textContent = "Sorry, your browser does not support location services.";
            }
        });

        // Step 3: This function runs if the location is found successfully
        function geolocationSuccess(position) {
            const userLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude,
            };

            // Center the map on the user's location
            map.setCenter(userLocation);
            map.setZoom(17); // Zoom in close

            // Create a marker at the user's location
            if (!marker) {
                marker = new google.maps.Marker({
                    map: map,
                });
            }
            marker.setPosition(userLocation);

            // Fill the hidden form fields with the coordinates
            latInput.value = userLocation.lat;
            lonInput.value = userLocation.lng;
            
            // Update the message and show the confirm button
            message.textContent = "We found you! Please check the pin on the map and click Confirm.";
            findMeButton.style.display = 'none'; // Hide the first button
            confirmButton.style.display = 'block'; // Show the confirm button
        }
        
        function geolocationError() {
            message.textContent = "Could not get your location. Please make sure you have allowed location permissions for your browser and try again.";
        }

        // Initialize the map when the page loads
        initMap();
    </script>
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap"></script>

</body>
</html>