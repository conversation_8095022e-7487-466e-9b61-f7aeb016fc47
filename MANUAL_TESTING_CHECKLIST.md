# Manual Testing Checklist for HTML Geolocation App

Use this checklist to systematically test your geolocation application across different scenarios and browsers.

## Pre-Testing Setup

- [ ] Server is running (`python3 server.py` or `node server.js`)
- [ ] Google Maps API key is configured in `geonorm.html`
- [ ] Browser developer tools are open (F12) to monitor console errors

## Basic Functionality Tests

### Initial Page Load
- [ ] Page loads without JavaScript errors in console
- [ ] Page title shows "Share Your Location"
- [ ] Map container is visible (gray background if no API key)
- [ ] "Find My Exact Location" button is visible and clickable
- [ ] "Confirm & Send Location" button is hidden initially
- [ ] Message shows: "Please click the button below so we can find your precise location using your phone's GPS."

### Google Maps Integration
- [ ] Map loads correctly (requires valid API key)
- [ ] Map shows default location (Atlanta: 33.7490, -84.3880)
- [ ] Map controls (zoom, pan) work properly
- [ ] No API key errors in browser console

## Geolocation Success Flow

### Location Permission Granted
- [ ] Click "Find My Exact Location" button
- [ ] Browser prompts for location permission
- [ ] Grant location permission
- [ ] Message changes to "Finding your location..."
- [ ] After location found, message changes to "We found you! Please check the pin on the map and click Confirm."
- [ ] Map centers on user's actual location
- [ ] Map zooms to level 17 (close zoom)
- [ ] Red marker appears at user's location
- [ ] "Find My Exact Location" button disappears
- [ ] "Confirm & Send Location" button appears
- [ ] Hidden form fields are populated with coordinates

### Form Submission
- [ ] Click "Confirm & Send Location" button
- [ ] Form submits successfully
- [ ] Redirects to success page showing received coordinates
- [ ] Success page displays correct latitude and longitude
- [ ] Server logs show received location data
- [ ] `received_locations.json` file is created/updated

## Error Handling Tests

### Location Permission Denied
- [ ] Click "Find My Exact Location" button
- [ ] Deny location permission when prompted
- [ ] Error message appears: "Could not get your location. Please make sure you have allowed location permissions for your browser and try again."
- [ ] "Find My Exact Location" button remains visible
- [ ] "Confirm & Send Location" button stays hidden
- [ ] No JavaScript errors in console

### Location Services Disabled
- [ ] Disable location services in browser settings
- [ ] Click "Find My Exact Location" button
- [ ] Appropriate error message is displayed
- [ ] Application doesn't crash or show JavaScript errors

### Network/API Errors
- [ ] Test with invalid Google Maps API key
- [ ] Test with no internet connection
- [ ] Test with server stopped (form submission should fail gracefully)

## Browser Compatibility Tests

Test the complete flow in each browser:

### Desktop Browsers
- [ ] **Chrome/Chromium** (latest version)
  - [ ] Basic functionality works
  - [ ] Geolocation permission prompt appears
  - [ ] Maps load correctly
  - [ ] Form submission works

- [ ] **Firefox** (latest version)
  - [ ] Basic functionality works
  - [ ] Geolocation permission prompt appears
  - [ ] Maps load correctly
  - [ ] Form submission works

- [ ] **Safari** (macOS)
  - [ ] Basic functionality works
  - [ ] Geolocation permission prompt appears
  - [ ] Maps load correctly
  - [ ] Form submission works

- [ ] **Edge** (latest version)
  - [ ] Basic functionality works
  - [ ] Geolocation permission prompt appears
  - [ ] Maps load correctly
  - [ ] Form submission works

### Mobile Browsers
- [ ] **Mobile Chrome** (Android)
  - [ ] Responsive design works
  - [ ] Touch interactions work
  - [ ] GPS accuracy is reasonable
  - [ ] Permission prompts work

- [ ] **Mobile Safari** (iOS)
  - [ ] Responsive design works
  - [ ] Touch interactions work
  - [ ] GPS accuracy is reasonable
  - [ ] Permission prompts work

## Security and Privacy Tests

### HTTPS vs HTTP
- [ ] Test on localhost (HTTP) - should work
- [ ] Test on remote HTTP server - geolocation should be blocked
- [ ] Test on HTTPS - should work

### Private/Incognito Mode
- [ ] Test in Chrome Incognito mode
- [ ] Test in Firefox Private mode
- [ ] Test in Safari Private mode
- [ ] Note: Some browsers may block geolocation in private mode

## Performance Tests

### Loading Performance
- [ ] Page loads quickly (< 3 seconds)
- [ ] Maps API loads without significant delay
- [ ] No unnecessary network requests

### Geolocation Performance
- [ ] Location is found within reasonable time (< 10 seconds)
- [ ] Accuracy is appropriate for the use case
- [ ] No excessive battery drain on mobile

## Accessibility Tests

### Keyboard Navigation
- [ ] Can navigate to buttons using Tab key
- [ ] Can activate buttons using Enter/Space
- [ ] Focus indicators are visible

### Screen Reader Compatibility
- [ ] Button labels are descriptive
- [ ] Status messages are announced
- [ ] Form fields have proper labels

## Edge Cases and Stress Tests

### Multiple Rapid Clicks
- [ ] Click "Find My Exact Location" multiple times rapidly
- [ ] Application should handle gracefully without duplicate requests

### Browser Refresh During Process
- [ ] Refresh page while location is being found
- [ ] Refresh page after location is found but before submission
- [ ] Application should reset to initial state

### Form Manipulation
- [ ] Manually edit hidden form fields in developer tools
- [ ] Submit form with modified coordinates
- [ ] Server should receive the modified data

## Test Results Documentation

For each test scenario, document:
- **Browser/Device**: Which browser and version
- **Result**: Pass/Fail
- **Notes**: Any issues or observations
- **Screenshots**: For any failures or unexpected behavior

### Sample Test Log Entry
```
Date: 2024-01-15
Browser: Chrome 120.0.6099.109
Test: Basic geolocation flow
Result: PASS
Notes: Location found in 3.2 seconds, accuracy ~10 meters
Coordinates: 33.7490, -84.3880
```

## Troubleshooting Common Issues

### Maps Don't Load
1. Check API key is valid and properly configured
2. Verify Maps JavaScript API is enabled in Google Cloud Console
3. Check for CORS or network errors in browser console

### Geolocation Fails
1. Ensure page is served over HTTPS or localhost
2. Check browser location permissions
3. Try in different browser or device
4. Check if location services are enabled system-wide

### Form Submission Fails
1. Verify server is running and accessible
2. Check network tab in developer tools for failed requests
3. Review server logs for errors
4. Test with curl: `curl -X POST -d "latitude=33.7490&longitude=-84.3880" http://localhost:8000/save_location`

## Automated Testing Integration

After manual testing:
- [ ] Run automated tests: `npm test`
- [ ] Compare automated results with manual findings
- [ ] Document any discrepancies
- [ ] Update automated tests based on manual test findings
